<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;

class UserManagement extends Component
{
    use WithPagination; // Untuk paginasi data

    public $search = '';
    public $perPage = 10;

    // Properti untuk form
    public $userId;
    public $name;
    public $email;
    public $role;
    public $password;
    public $password_confirmation;

    public $showUserModal = false; // Untuk menampilkan/menyembunyikan modal form
    public $modalTitle = ''; // Judul modal (Add User / Edit User)

    public function mount()
    {
        // Berikan nilai default untuk properti form saat komponen pertama kali dimuat
        $this->name = '';
        $this->email = '';
        $this->role = ''; // Penting untuk dropdown, berikan string kosong atau nilai default yang valid
        $this->password = '';
        $this->password_confirmation = '';
    }

    // Aturan validasi
    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($this->userId),
            ],
            'role' => 'required|string|in:admin,editor,user',
            'password' => $this->userId ? 'nullable|min:8|confirmed' : 'required|min:8|confirmed',
        ];
    }

    // Reset properti form ketika modal ditutup atau untuk form baru
    public function resetForm()
    {
        // Pastikan 'role' direset ke string kosong agar dropdown tidak default memilih opsi yang tidak valid
        $this->reset(['userId', 'name', 'email', 'role', 'password', 'password_confirmation', 'modalTitle']);
        // Setelah reset, inisialisasi kembali properti role agar tidak undefine saat modal dibuka lagi
        $this->role = '';
    }
    // Tampilkan modal untuk menambah user baru
    public function createUser()
    {
        $this->resetForm();
        $this->modalTitle = 'Tambah Pengguna Baru';
        $this->showUserModal = true;
    }

    // Simpan user baru atau update user yang sudah ada
    public function saveUser()
    {
        $this->validate();

        $data = [
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
        ];

        if (!empty($this->password)) {
            $data['password'] = Hash::make($this->password);
        }

        if ($this->userId) {
            // Update user
            User::find($this->userId)->update($data);
            session()->flash('message', 'Pengguna berhasil diperbarui.');
        } else {
            // Buat user baru
            User::create($data);
            session()->flash('message', 'Pengguna berhasil ditambahkan.');
        }

        $this->showUserModal = false;
        $this->resetForm();
        $this->dispatch('close-modal'); // Dispatch event untuk menutup modal jika menggunakan JS
    }

    // Tampilkan modal untuk mengedit user
    public function editUser(User $user)
    {
        $this->resetForm();
        $this->userId = $user->id;
        $this->name = $user->name;
        $this->email = $user->email;
        $this->role = $user->role;
        $this->modalTitle = 'Edit Pengguna';
        $this->showUserModal = true;
    }

    // Hapus user
    public function deleteUser(User $user)
    {
        $user->delete();
        session()->flash('message', 'Pengguna berhasil dihapus.');
    }

    // Render view komponen
    public function render()
    {
        $users = User::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhere('role', 'like', '%' . $this->search . '%');
            })
            ->orderBy('name')
            ->paginate($this->perPage);

        return view('livewire.user-management', [
            'users' => $users,
        ])->layout('layouts.app');
    }
}
