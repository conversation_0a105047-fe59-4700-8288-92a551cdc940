<div>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            Manajemen Pengguna
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">

                    @if (session()->has('message'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline">{{ session('message') }}</span>
                    </div>
                    @endif

                    <div class="flex justify-between items-center mb-4">
                        <input type="text" wire:model.live.debounce.300ms="search" placeholder="Cari pengguna..."
                            class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
                        <x-primary-button wire:click="createUser">
                            Tambah Pengguna
                        </x-primary-button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nama</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse ($users as $user)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">{{ $user->name }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">{{ $user->email }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">{{ $user->role }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <x-secondary-button wire:click="editUser({{ $user->id }})" class="mr-2">Edit</x-secondary-button>
                                        <x-danger-button wire:click="deleteUser({{ $user->id }})" wire:confirm="Apakah Anda yakin ingin menghapus pengguna ini?">Hapus</x-danger-button>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">Tidak ada pengguna ditemukan.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $users->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal untuk Tambah/Edit Pengguna --}}
    <x-modal name="user-modal" wire:model="showUserModal" maxWidth="md">
        <form wire:submit.prevent="saveUser">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $modalTitle }}</h3>

            <div class="mb-4">
                <x-input-label for="name" value="Nama" />
                <x-text-input id="name" type="text" class="mt-1 block w-full !bg-white !text-gray-900 !border-gray-300" wire:model="name" required autofocus />
                <x-input-error class="mt-2" :messages="$errors->get('name')" />
            </div>

            <div class="mb-4">
                <x-input-label for="email" value="Email" />
                <x-text-input id="email" type="email" class="mt-1 block w-full !bg-white !text-gray-900 !border-gray-300" wire:model="email" required />
                <x-input-error class="mt-2" :messages="$errors->get('email')" />
            </div>

            <div class="mb-4">
                <x-input-label for="role" value="Role" />
                <select id="role" wire:model="role" class="border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm mt-1 block w-full !bg-white !text-gray-900 !border-gray-300" required>
                    <option value="">Pilih Role</option>
                    <option value="admin">Admin</option>
                    <option value="editor">Editor</option>
                    <option value="user">User</option>
                </select>
                <x-input-error class="mt-2" :messages="$errors->get('role')" />
            </div>

            <div class="mb-4">
                <x-input-label for="password" value="Password" />
                <x-text-input id="password" type="password" class="mt-1 block w-full !bg-white !text-gray-900 !border-gray-300" wire:model="password" :required="!$userId" />
                @if ($userId)
                <p class="text-sm text-gray-600 mt-1">Kosongkan jika tidak ingin mengubah password.</p>
                @endif
                <x-input-error class="mt-2" :messages="$errors->get('password')" />
            </div>

            <div class="mb-4">
                <x-input-label for="password_confirmation" value="Konfirmasi Password" />
                <x-text-input id="password_confirmation" type="password" class="mt-1 block w-full !bg-white !text-gray-900 !border-gray-300" wire:model="password_confirmation" :required="!$userId" />
                <x-input-error class="mt-2" :messages="$errors->get('password_confirmation')" />
            </div>

            <div class="mt-6 flex justify-end">
                <x-secondary-button wire:click="$set('showUserModal', false)" class="mr-2">Batal</x-secondary-button>
                <x-primary-button type="submit">Simpan</x-primary-button>
            </div>
        </form>
    </x-modal>
</div>