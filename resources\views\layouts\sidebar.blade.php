<aside class="w-64 bg-gray-800 text-white min-h-screen p-4 flex flex-col">
    <div class="flex items-center justify-center h-16">
        <a href="{{ route('dashboard') }}" class="text-2xl font-semibold text-white">
            {{ config('app.name', 'Laravel') }}
        </a>
    </div>

    <nav class="mt-8 flex-grow">
        <ul>
            <li class="mb-2">
                <x-sidebar-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                    <i class="fas fa-tachometer-alt mr-2"></i> {{-- Icon Dashboard (butuh Font Awesome) --}}
                    Dashboard
                </x-sidebar-link>
            </li>

            @auth
            @if (Auth::user()->isAdmin())
            <li class="mb-2">
                <x-sidebar-link :href="route('admin.users.index')" :active="request()->routeIs('admin.users.index')">
                    <i class="fas fa-users mr-2"></i> {{-- Icon Users (butuh Font Awesome) --}}
                    Manajemen Pengguna
                </x-sidebar-link>
            </li>
            {{-- Tambahkan menu admin lainnya di sini --}}
            @endif
            {{-- Tambahkan menu lain yang tidak hanya admin di sini, jika ada --}}
            @endauth

            {{-- Contoh menu lain (non-admin) --}}
            {{-- <li class="mb-2">
                <x-sidebar-link :href="route('profile.edit')" :active="request()->routeIs('profile.edit')">
                    <i class="fas fa-user mr-2"></i> Profil
                </x-sidebar-link>
            </li> --}}
        </ul>
    </nav>

    {{-- Logout Link di bawah --}}
    <div class="mt-auto">
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <x-sidebar-link :href="route('logout')"
                onclick="event.preventDefault(); this.closest('form').submit();">
                <i class="fas fa-sign-out-alt mr-2"></i> Logout
            </x-sidebar-link>
        </form>
    </div>
</aside>