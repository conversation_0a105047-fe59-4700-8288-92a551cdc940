<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Stocker - Stock Market Website Template</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Roboto:wght@400;500;700;900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <link href="{{ asset('stock-market/lib/animate/animate.min.css') }}" rel="stylesheet">
    <link href="{{ asset('stock-market/lib/lightbox/css/lightbox.min.css') }}" rel="stylesheet">
    <link href="{{ asset('stock-market/lib/owlcarousel/assets/owl.carousel.min.css') }}" rel="stylesheet">

    <link href="{{ asset('stock-market/css/bootstrap.min.css') }}" rel="stylesheet">

    <link href="{{ asset('stock-market/css/style.css') }}" rel="stylesheet">

    <style>
        .card-stock-analysis {
            border: none;
            border-radius: 1rem;
            overflow: hidden;
        }

        .card-body-stock-analysis {
            padding: 2.5rem;
        }

        .form-control-stock-analysis {
            width: 100%;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.5rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-control-stock-analysis:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
        }

        .btn-stock-analysis {
            background-color: #007bff;
            color: #fff;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 1.1rem;
            transition: background-color 0.15s ease-in-out;
        }

        .btn-stock-analysis:hover {
            background-color: #0056b3;
            color: #fff;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #fff;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .alert-stock-analysis-success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-stock-analysis-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>

    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <div class="container-fluid topbar bg-light px-5 d-none d-lg-block">
        <div class="row gx-0 align-items-center">
            <div class="col-lg-8 text-center text-lg-start mb-2 mb-lg-0">
            </div>
            <div class="col-lg-4 text-center text-lg-end">
                <div class="d-inline-flex align-items-center" style="height: 45px;">
                    @guest {{-- Tampilkan ini jika user belum login --}}
                    <!-- <a href="{{ route('register') }}" class="text-muted small me-4"><i class="fa fa-user text-primary me-2"></i>Register</small></a> -->
                    <a href="{{ route('login') }}" class="text-muted small me-3"><i class="fa fa-sign-in-alt text-primary me-2"></i>Login</small></a>
                    @endguest

                    @auth {{-- Tampilkan ini jika user sudah login --}}
                    <div class="dropdown">
                        <a href="#" class="dropdown-toggle text-dark" data-bs-toggle="dropdown"><small><i class="fa fa-home text-primary me-2"></i> My Dashboard</small></a>
                        <div class="dropdown-menu rounded">
                            <a href="{{ route('dashboard') }}" class="dropdown-item"><i class="fas fa-chart-line me-2"></i> Dashboard</a> {{-- Link ke dashboard utama --}}
                            <a href="{{ route('profile.edit') }}" class="dropdown-item"><i class="fas fa-user-alt me-2"></i> My Profile</a>
                            {{-- <a href="#" class="dropdown-item"><i class="fas fa-comment-alt me-2"></i> Inbox</a> --}}
                            {{-- <a href="#" class="dropdown-item"><i class="fas fa-bell me-2"></i> Notifications</a> --}}
                            {{-- <a href="#" class="dropdown-item"><i class="fas fa-cog me-2"></i> Account Settings</a> --}}

                            <hr class="dropdown-divider">

                            {{-- Form Logout --}}
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <a href="{{ route('logout') }}" class="dropdown-item"
                                    onclick="event.preventDefault(); this.closest('form').submit();">
                                    <i class="fas fa-power-off me-2"></i> Log Out
                                </a>
                            </form>
                        </div>
                    </div>
                    @endauth
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid position-relative p-0">
        <nav class="navbar navbar-expand-lg navbar-light px-4 px-lg-5 py-3 py-lg-0">
            <a href="{{ route('home') }}" class="navbar-brand p-0">
                <h1 class="text-primary"><i class="fas fa-search-dollar me-3"></i>Stocker</h1>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
                <span class="fa fa-bars"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <div class="navbar-nav ms-auto py-0">
                    <a href="{{ route('home') }}" class="nav-item nav-link">Home</a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link active" data-bs-toggle="dropdown">
                            <span class="dropdown-toggle">Analisa</span>
                        </a>
                        <div class="dropdown-menu m-0">
                            <a href="{{ route('analisa-saham') }}" class="dropdown-item">Saham</a>
                            <a href="{{ route('analisa-crypto') }}" class="dropdown-item">Crypto</a>
                        </div>
                    </div>
                    <a href="#services" class="nav-item nav-link">Services</a>
                    <a href="#blogs" class="nav-item nav-link">Blogs</a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link" data-bs-toggle="dropdown">
                            <span class="dropdown-toggle">Pages</span>
                        </a>
                        <div class="dropdown-menu m-0">
                            <a href="#features" class="dropdown-item">Our Features</a>
                            <a href="#team" class="dropdown-item">Our team</a>
                            <a href="#testimonials" class="dropdown-item">Testimonial</a>
                            <a href="#offer" class="dropdown-item">Our offer</a>
                            <a href="#faqs" class="dropdown-item">FAQs</a>
                            <a href="#" class="dropdown-item">404 Page</a>
                        </div>
                    </div>
                    <a href="#contact" class="nav-item nav-link">Contact Us</a>
                </div>
                <a href="{{ route('login') }}" class="btn btn-primary rounded-pill py-2 px-4 my-3 my-lg-0 flex-shrink-0">Get Started</a>
            </div>
        </nav>

        <div class="container-fluid bg-breadcrumb">
            <div class="container text-center py-5" style="max-width: 900px;">
                <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">Analisa Saham</h4>
                <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="#">Analisa</a></li>
                    <li class="breadcrumb-item active text-primary">Saham</li>
                </ol>
            </div>
        </div>
    </div>
    <div class="container-fluid bg-light py-5">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-6 wow fadeInUp" data-wow-delay="0.3s">
                    <div class="card card-stock-analysis shadow-lg">
                        <div class="card-body card-body-stock-analysis">
                            <h1 class="text-center mb-4 text-primary">Analisis Saham by AI</h1>

                            <div class="mb-3">
                                <label for="stockSymbol" class="form-label fw-semibold">
                                    Masukkan Simbol Saham (misal: BBCA, TLKM):
                                </label>
                                <input
                                    type="text"
                                    id="stockSymbol"
                                    placeholder="Contoh: BBCA"
                                    class="form-control form-control-stock-analysis" />
                            </div>

                            <div class="mb-3">
                                <label for="currentPrice" class="form-label fw-semibold">
                                    Harga Sekarang (misal: 7500):
                                </label>
                                <input
                                    type="number"
                                    id="currentPrice"
                                    placeholder="Contoh: 7500"
                                    class="form-control form-control-stock-analysis" />
                            </div>

                            <div class="mb-3">
                                <label for="currentBVPS" class="form-label fw-semibold">
                                    Current BVPS (opsional, misal: 1500):
                                </label>
                                <input
                                    type="number"
                                    id="currentBVPS"
                                    placeholder="Opsional: 1500"
                                    class="form-control form-control-stock-analysis" />
                            </div>

                            <div class="mb-4">
                                <label for="meanPBV" class="form-label fw-semibold">
                                    Mean PBV 3 Tahun (opsional, misal: 2.5):
                                </label>
                                <input
                                    type="number"
                                    id="meanPBV"
                                    placeholder="Opsional: 2.5"
                                    step="0.01"
                                    class="form-control form-control-stock-analysis" />
                            </div>

                            <button
                                id="analyzeButton"
                                class="btn btn-primary btn-stock-analysis w-100 d-flex align-items-center justify-content-center">
                                <span id="buttonText">Analisa</span>
                                <div id="loadingSpinner" class="spinner ms-2 hidden"></div>
                            </button>

                            <div id="results" class="mt-4 p-3 border rounded hidden alert-stock-analysis-success">
                                <h2 class="fs-5 fw-semibold text-primary mb-3">Hasil Analisis:</h2>
                                <div id="analysisContent" class="text-secondary lh-base">
                                </div>
                                <p class="text-muted mt-3 fst-italic small">
                                    *Analisis ini disimulasikan oleh model AI dan bukan merupakan nasihat keuangan nyata. Selalu lakukan riset Anda sendiri sebelum membuat keputusan investasi.
                                </p>
                            </div>

                            <div id="errorMessage" class="mt-4 p-3 rounded hidden alert-stock-analysis-danger">
                                <p class="fw-semibold">Terjadi Kesalahan:</p>
                                <p id="errorContent"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid footer py-5 wow fadeIn" data-wow-delay="0.2s">
        <div class="container py-5 border-start-0 border-end-0" style="border: 1px solid; border-color: rgb(255, 255, 255, 0.08);">
            <div class="row g-5">
                <div class="col-md-6 col-lg-6 col-xl-4">
                    <div class="footer-item">
                        <a href="{{ route('home') }}" class="p-0">
                            <h4 class="text-white"><i class="fas fa-search-dollar me-3"></i>Stocker</h4>
                        </a>
                        <p class="mb-4">Dolor amet sit justo amet elitr clita ipsum elitr est.Lorem ipsum dolor sit amet, consectetur adipiscing...</p>
                        <div class="d-flex">
                            <a href="#" class="bg-primary d-flex rounded align-items-center py-2 px-3 me-2">
                                <i class="fas fa-apple-alt text-white"></i>
                                <div class="ms-3">
                                    <small class="text-white">Download on the</small>
                                    <h6 class="text-white">App Store</h6>
                                </div>
                            </a>
                            <a href="#" class="bg-dark d-flex rounded align-items-center py-2 px-3 ms-2">
                                <i class="fas fa-play text-primary"></i>
                                <div class="ms-3">
                                    <small class="text-white">Get it on</small>
                                    <h6 class="text-white">Google Play</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-2">
                    <div class="footer-item">
                        <h4 class="text-white mb-4">Quick Links</h4>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> About Us</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Feature</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Attractions</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Tickets</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Blog</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Contact us</a>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-3">
                    <div class="footer-item">
                        <h4 class="text-white mb-4">Support</h4>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Privacy Policy</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Terms & Conditions</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Disclaimer</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Support</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> FAQ</a>
                        <a href="#"><i class="fas fa-angle-right me-2"></i> Help</a>
                    </div>
                </div>
                <div class="col-md-6 col-lg-6 col-xl-3">
                    <div class="footer-item">
                        <h4 class="text-white mb-4">Contact Info</h4>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-map-marker-alt text-primary me-3"></i>
                            <p class="text-white mb-0">123 Street New York.USA</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-envelope text-primary me-3"></i>
                            <p class="text-white mb-0"><EMAIL></p>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fa fa-phone-alt text-primary me-3"></i>
                            <p class="text-white mb-0">(+012) 3456 7890</p>
                        </div>
                        <div class="d-flex align-items-center mb-4">
                            <i class="fab fa-firefox-browser text-primary me-3"></i>
                            <p class="text-white mb-0"><EMAIL></p>
                        </div>
                        <div class="d-flex">
                            <a class="btn btn-primary btn-sm-square rounded-circle me-3" href="#"><i class="fab fa-facebook-f text-white"></i></a>
                            <a class="btn btn-primary btn-sm-square rounded-circle me-3" href="#"><i class="fab fa-twitter text-white"></i></a>
                            <a class="btn btn-primary btn-sm-square rounded-circle me-3" href="#"><i class="fab fa-instagram text-white"></i></a>
                            <a class="btn btn-primary btn-sm-square rounded-circle me-0" href="#"><i class="fab fa-linkedin-in text-white"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid copyright py-4">
        <div class="container">
            <div class="row g-4 align-items-center">
                <div class="col-md-6 text-center text-md-start mb-md-0">
                    <span class="text-body"><a href="#" class="border-bottom text-white"><i class="fas fa-copyright text-light me-2"></i>Your Site Name</a>, All right reserved.</span>
                </div>
                <div class="col-md-6 text-center text-md-end text-body">
                    Designed By <a class="border-bottom text-white" href="https://htmlcodex.com">HTML Codex</a>
                </div>
            </div>
        </div>
    </div>
    <a href="#" class="btn btn-primary btn-lg-square rounded-circle back-to-top"><i class="fa fa-arrow-up"></i></a>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ asset('stock-market/lib/wow/wow.min.js') }}"></script>
    <script src="{{ asset('stock-market/lib/easing/easing.min.js') }}"></script>
    <script src="{{ asset('stock-market/lib/waypoints/waypoints.min.js') }}"></script>
    <script src="{{ asset('stock-market/lib/counterup/counterup.min.js') }}"></script>
    <script src="{{ asset('stock-market/lib/lightbox/js/lightbox.min.js') }}"></script>
    <script src="{{ asset('stock-market/lib/owlcarousel/owl.carousel.min.js') }}"></script>

    <script src="{{ asset('stock-market/js/main.js') }}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const stockSymbolInput = document.getElementById('stockSymbol');
            const currentPriceInput = document.getElementById('currentPrice');
            const currentBVPSInput = document.getElementById('currentBVPS');
            const meanPBVInput = document.getElementById('meanPBV');
            const analyzeButton = document.getElementById('analyzeButton');
            const buttonText = document.getElementById('buttonText');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const resultsDiv = document.getElementById('results');
            const analysisContentDiv = document.getElementById('analysisContent');
            const errorMessageDiv = document.getElementById('errorMessage');
            const errorContentP = document.getElementById('errorContent');

            analyzeButton.addEventListener('click', async () => {
                try {
                    const symbol = stockSymbolInput.value.trim().toUpperCase();
                    const currentPrice = parseFloat(currentPriceInput.value.trim());
                    const currentBVPS = parseFloat(currentBVPSInput.value.trim());
                    const meanPBV = parseFloat(meanPBVInput.value.trim());

                    if (!symbol) {
                        displayError('Mohon masukkan simbol saham.');
                        return;
                    }
                    if (isNaN(currentPrice) || currentPrice <= 0) {
                        displayError('Mohon masukkan harga sekarang yang valid (angka positif).');
                        return;
                    }

                    // Reset tampilan
                    resultsDiv.classList.add('hidden');
                    errorMessageDiv.classList.add('hidden');
                    analysisContentDiv.innerHTML = '';
                    errorContentP.innerHTML = '';

                    // Tampilkan loading
                    buttonText.textContent = 'Menganalisa...';
                    loadingSpinner.classList.remove('hidden');
                    analyzeButton.disabled = true;

                    let fairValuePromptPart = "";
                    let fairValueCalculated = null; // To store calculated fair value
                    if (!isNaN(currentBVPS) && currentBVPS > 0 && !isNaN(meanPBV) && meanPBV > 0) {
                        fairValueCalculated = currentBVPS * meanPBV;
                        fairValuePromptPart = `Gunakan Current BVPS ${currentBVPS} dan Mean PBV 3 Tahun ${meanPBV} untuk mengestimasi harga wajar. Harga wajar yang dihitung adalah ${fairValueCalculated.toFixed(2)}.`;
                    } else {
                        fairValuePromptPart = `Cari tahu Current BVPS dan Mean PBV 3 Tahun terakhir dari saham ini untuk mengestimasi harga wajar. Pastikan untuk menyebutkan Mean PBV selama 3 tahun terakhir dalam analisis Anda.`;
                    }

                    const prompt = `Sebagai analis keuangan yang berpengalaman, lakukan analisis fundamental singkat untuk saham dengan simbol ${symbol} dengan harga saat ini ${currentPrice}.
                    ${fairValuePromptPart}
                    Berdasarkan perbandingan harga saat ini (${currentPrice}) dengan harga wajar yang diestimasi, simpulkan apakah saham ini saat ini "OVERVALUED" atau "UNDERVALUED".
                    Sertakan nilai Margin Of Safety yang dihitung berdasarkan harga saat ini dan harga wajar.
                    Lakukan juga analisis teknikal singkat untuk menemukan support terdekat berdasarkan LL (Lower Low) dan HL (Higher Low) yang dapat menjadi rekomendasi untuk titik entry.
                    Setelah itu, berikan rekomendasi apakah "BELI" atau "JANGAN BELI" berdasarkan analisis fundamental dan teknikal Anda secara keseluruhan.
                    **Pastikan untuk menyoroti poin-poin berikut dalam respons Anda agar mudah ditemukan:**
                    1. **Harga Wajar:** (Nilai estimasi harga wajar)
                    2. **Margin of Safety:** (Persentase MOS)
                    3. **Valuasi:** (OVERVALUED/UNDERVALUED)
                    4. **Support Terdekat untuk Beli:** (Nilai support teknikal)
                    5. **Rekomendasi Keseluruhan:** (BELI/JANGAN BELI)
                    Jelaskan secara ringkas alasan di balik rekomendasi Anda, dengan mempertimbangkan faktor-faktor seperti kesehatan keuangan (misalnya, profitabilitas, utang), potensi pertumbuhan, posisi pasar, valuasi, dan level support teknikal.
                    Jika simbol saham tidak dikenal atau tidak valid, nyatakan bahwa Anda tidak dapat menganalisisnya.`;

                    let chatHistory = [];
                    chatHistory.push({
                        role: "user",
                        parts: [{
                            text: prompt
                        }]
                    });

                    const payload = {
                        contents: chatHistory
                    };

                    const apiKey = "{{ env('GEMINI_API_KEY') }}";
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;

                    const MAX_RETRIES = 3;
                    let retries = 0;
                    let response;

                    while (retries < MAX_RETRIES) {
                        try {
                            response = await fetch(apiUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(payload)
                            });

                            if (response.ok) {
                                break;
                            } else if (response.status === 429) {
                                const delay = Math.pow(2, retries) * 1000 + Math.random() * 1000;
                                console.warn(`Too many requests. Retrying in ${delay / 1000} seconds...`);
                                await new Promise(resolve => setTimeout(resolve, delay));
                                retries++;
                            } else {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                        } catch (fetchError) {
                            console.error("Fetch error:", fetchError);
                            if (retries === MAX_RETRIES - 1) {
                                throw fetchError;
                            }
                            const delay = Math.pow(2, retries) * 1000 + Math.random() * 1000;
                            console.warn(`Network error. Retrying in ${delay / 1000} seconds...`);
                            await new Promise(resolve => setTimeout(resolve, delay));
                            retries++;
                        }
                    }

                    if (!response || !response.ok) {
                        throw new Error("Gagal mendapatkan respons dari Gemini setelah beberapa kali percobaan.");
                    }

                    const result = await response.json();

                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        let analysisText = result.candidates[0].content.parts[0].text;

                        // Calculate and append Margin of Safety if fairValueCalculated is available
                        if (fairValueCalculated && fairValueCalculated > 0) {
                            const marginOfSafety = (1 - (currentPrice / fairValueCalculated)) * 100;
                            // Injecting MOS into the analysisText directly after the AI's response for better control
                            analysisText += `<br><br><strong>Margin of Safety:</strong> ${marginOfSafety.toFixed(2)}%`;
                        } else {
                            analysisText += `<br><br><strong>Margin of Safety:</strong> Tidak dapat dihitung (BVPS atau Mean PBV tidak tersedia/valid).`;
                        }


                        analysisContentDiv.innerHTML = formatAnalysisText(analysisText);
                        resultsDiv.classList.remove('hidden');
                    } else {
                        displayError('Tidak dapat menghasilkan analisis untuk simbol saham ini. Coba simbol lain.');
                    }
                } catch (error) {
                    console.error('Error during analysis:', error);
                    displayError(`Gagal menganalisis saham: ${error.message}`);
                } finally {
                    buttonText.textContent = 'Analisa';
                    loadingSpinner.classList.add('hidden');
                    analyzeButton.disabled = false;
                }
            });

            function displayError(message) {
                errorContentP.textContent = message;
                errorMessageDiv.classList.remove('hidden');
                resultsDiv.classList.add('hidden'); // Hide results if an error occurs
            }

            function formatAnalysisText(text) {
                let formattedText = text.replace(/\n/g, '<br>');
                formattedText = formattedText.replace(/BELI/g, '<strong class="text-success">BELI</strong>'); // Bootstrap green
                formattedText = formattedText.replace(/JANGAN BELI/g, '<strong class="text-danger">JANGAN BELI</strong>'); // Bootstrap red
                formattedText = formattedText.replace(/OVERVALUED/g, '<strong class="text-danger">OVERVALUED</strong>'); // Bootstrap red
                formattedText = formattedText.replace(/UNDERVALUED/g, '<strong class="text-success">UNDERVALUED</strong>'); // Bootstrap green
                formattedText = formattedText.replace(/Support terdekat/gi, '<strong class="text-info">Support terdekat</strong>'); // Bootstrap info blue
                formattedText = formattedText.replace(/Harga Wajar:/gi, '<strong class="text-primary">Harga Wajar:</strong>'); // Highlight Harga Wajar
                formattedText = formattedText.replace(/Margin of Safety:/gi, '<strong class="text-primary">Margin of Safety:</strong>'); // Highlight Margin of Safety
                formattedText = formattedText.replace(/Valuasi:/gi, '<strong class="text-primary">Valuasi:</strong>'); // Highlight Valuasi
                formattedText = formattedText.replace(/Support Terdekat untuk Beli:/gi, '<strong class="text-primary">Support Terdekat untuk Beli:</strong>'); // Highlight Support Terdekat untuk Beli
                formattedText = formattedText.replace(/Rekomendasi Keseluruhan:/gi, '<strong class="text-primary">Rekomendasi Keseluruhan:</strong>'); // Highlight Rekomendasi Keseluruhan
                return formattedText;
            }
        });
    </script>
</body>

</html>
