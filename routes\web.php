<?php

use App\Http\Controllers\ProfileController;
use App\Livewire\UserManagement;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('frontend.home');
})->name('home');

Route::get('/about', function () {
    return view('frontend.about');
})->name('about');

Route::get('/analisa-saham', function () {
    return view('frontend.analisa-saham');
})->name('analisa-saham');

Route::get('/analisa-crypto', function () {
    return view('frontend.analisa-crypto');
})->name('analisa-crypto');

Route::get('/analisa-crypto-new', function () {
    return view('frontend.analisa-crypto-new');
})->name('analisa-crypto-new');

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::redirect('/profile', '/profile')->name('profile');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Rute Khusus Admin
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/admin/dashboard', function () {
        return view('dashboard'); // Anda bisa tetap ke dashboard atau buat view khusus admin
    })->name('admin.dashboard');

    // Rute untuk Manajemen Pengguna
    Route::get('/admin/users', UserManagement::class)->name('admin.users.index');
});

// Rute Khusus Editor
Route::middleware(['auth', 'hasRole:editor'])->group(function () {
    Route::get('/editor/dashboard', function () {
        return 'Selamat datang di halaman Editor!';
    })->name('editor.dashboard');

    // Contoh rute lain untuk editor
    // Route::get('/editor/posts/create', [EditorController::class, 'createPost'])->name('editor.posts.create');
});

// Rute Khusus Pengguna Biasa (jika ada halaman khusus untuk user biasa)
Route::middleware(['auth', 'hasRole:user'])->group(function () {
    Route::get('/user/settings', function () {
        return 'Pengaturan Pengguna Biasa.';
    })->name('user.settings');
});

require __DIR__ . '/auth.php';
