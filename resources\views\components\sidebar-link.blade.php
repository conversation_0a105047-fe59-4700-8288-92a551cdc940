@props(['active'])

@php
$classes = ($active ?? false)
? 'flex items-center px-4 py-2 text-sm font-semibold text-white bg-gray-700 rounded-md transition duration-150 ease-in-out'
: 'flex items-center px-4 py-2 text-sm font-semibold text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition duration-150 ease-in-out';
@endphp

<a {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</a>