<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat user Admin
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'), // Ganti dengan password yang kuat
            'role' => 'admin',
        ]);

        // Buat user Editor
        User::create([
            'name' => 'Editor User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'editor',
        ]);

        // Buat user Biasa
        User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
        ]);
    }
}
